using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Drawing;
using System.Linq;
using System.Net.Mail;
using System.Reflection;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Configuration;
using Microsoft.Extensions.Configuration;
using Excel = Microsoft.Office.Interop.Excel;
using MaxLib;
using log4net;
using log4net.Config;

[assembly: log4net.Config.XmlConfigurator(ConfigFile = "../../../LoggerConfig.xml")]

namespace PortalMitasCalcPNLAndInsertDailyPNL
{
    class Program
    {

        private const string ConfigFolder = @"\\maxvc1\d$\Visual Cron\Scripts\PortalMitasCalcPNLAndInsertDailyPNL";
        public static IConfiguration Configuration  //Connection to the Appsettings.json file to pull the configuration
        {
            get
            {
                var builder = new ConfigurationBuilder()
                .SetBasePath(ConfigFolder)
                .AddJsonFile("Appsettings.json", false, true);
                return builder.Build();
            }
        }

        // Define a static logger variable so that it references the Logger instance named "MyApp".
        private static readonly ILog logger = LogManager.GetLogger(typeof(Program));

        static void Main(string[] args)
        {
            try
            {

                logger.Info("-------------------------------------------");
                logger.Info("-------------------------------------------");
                logger.Info("Entering applicaiton.");

                logger.Info("Getting Trade Data from Staging Table");
                DataTable pnlStagingTable = ExecuteQuery(getPNLStagingQueryString(), getMAXSQL2_Mitas_SQLConnString());

                var distinctDates = pnlStagingTable.AsEnumerable()
                    .Select(row => row.Field<DateTime>("TradeDate").ToString("yyyy-MM-dd"))
                    .Distinct()
                    .ToList();

                var maxDate = distinctDates.Max();
                logger.Info("Trade Date: " + maxDate);

                logger.Info("Getting Open Positions from Carry Forward stored proc.");
                DataTable carryForwardTable = GetCarryForwardTable(maxDate);

                logger.Info("Calculating PNL");
                DataSet ds = calculatePNL(pnlStagingTable, carryForwardTable, DateTime.Parse(maxDate));
                DataTable pnlResultsTable = ds.Tables[0];
                DataTable carryForwardResultsTable = ds.Tables[1];

                ///////////////////////////////////////////////////////////

                // Truncate the staging table
                logger.Info("Truncating Staging Table");
                TruncateStagingTable();

                // Insert carryForwardAdjustedPositions to staging table
                logger.Info("Inserting updated carryForward positions to Staging Table");
                InsertToPNLStaging(carryForwardResultsTable);

                // Run UpdateCarryForwardToPNL SP
                logger.Info("Running UpdateCarryForwardToPNL Stored Proc");
                RunStoredProc("msp_UpdateCarryForwardPNL");

                ///////////////////////////////////////////////////////////

                // Truncate the staging table
                logger.Info("Truncating Staging Table");
                TruncateStagingTable();

                // Insert calculated PNL back into the staging table
                logger.Info("Inserting Calculated PNL into Staging Table");
                InsertToPNLStaging(pnlResultsTable);

                // Run InsertToPNL SP (where not exists)
                logger.Info("Running InsertToPNL Stored Procedure");
                RunStoredProc("msp_InsertDailyPNL");

                ///////////////////////////////////////////////////////////

                // Run Recon Stored Procedure
                logger.Info("Running Recon Stored Procedure");
                RunReconSP(maxDate);

                logger.Info("Exiting applicaiton.");
            }
            catch (Exception ex)
            {
                logger.Info("Error: " + ex);
                throw new Exception("An error occurred during the operation.", ex);
            }
        }

        private static DataSet calculatePNL(DataTable pnlStagingTable, DataTable carryForwardTable, DateTime maxDate)
        {

            // Create a new DataTable with the same schema as pnlStagingTable; will merge all updated changes to resultsTable at end
            DataTable resultsTable = pnlStagingTable.Clone();

            // Log the number of rows in pnlStagingTable
            logger.Info($"Number of rows in pnlStagingTable: {pnlStagingTable.Rows.Count}");

            // turn carryForwardTable into a list of DataRows for easier access
            List<DataRow> carryForwardRows = carryForwardTable.AsEnumerable().ToList();

            // Log the number of rows in carryForwardTable
            logger.Info($"Number of rows in carryForwardTable: {carryForwardTable.Rows.Count}");

            // Group trades by AccountNumber, TradeDate, and Cusip
            var groupedTrades = pnlStagingTable.AsEnumerable()
                .GroupBy(row => new
                {
                    BookCode = row.Field<string>("BookCode"),
                    //AccountNumber = row.Field<string>("AccountNumber"),
                    TradeDate = row.Field<DateTime>("TradeDate"),
                    Cusip = row.Field<string>("Cusip"),
                    Symbol = row.Field<string>("Symbol"), //this should help for when there are null cusips (without symbol here, we saw null cusips get put in group 1)
                })
                .ToList();

            int groupID = 1;
            logger.Info("Number of groups: " + groupedTrades.Count());

            // Get max(SequenceNumber) from tblPNL table in DB
            logger.Info("Getting max(SequenceNumber) from tblPNL table in DB");
            int maxSequenceNumber = GetMaxSequenceNumberFromPNLTable();
            maxSequenceNumber++; // Increment to start from the next number

            foreach (var group in groupedTrades)
            {
                logger.Info("Current Group ID: " + groupID);

                List<DataRow> thisGroup = group.ToList();

                // Assign GroupID to each row in the group
                foreach (var row in thisGroup)
                {
                    row.SetField<int>("GroupID", groupID);
                }
                groupID++;

                // Find trades within 2 seconds of each other
                //logger.Info("Starting special trade matching process for group! (Find Opposing Trades within groups (buy and sell) within 2 seconds of each other with same quantities and pair them off)");
                int seconds = 2;
                FindTradesWithinXSeconds(thisGroup, seconds);
                //logger.Info("Finished special trade matching process for group!");

                // Order by execution time
                thisGroup = thisGroup.OrderBy(row => row.Field<string>("ExecutionTime")).ToList();

                // if group has trades
                if (thisGroup.Any())
                {

                    decimal cumulativeQuantity = 0;
                    decimal costBasis = 0;


                    // Get open positions from carryForwardTable for this group
                    List<DataRow> openPositions = findOpenPositions(thisGroup, carryForwardRows);
                    foreach (var openRow in openPositions)
                    {
                        // logger.Info(
                        //     $"OpenPosition - GroupID: {groupID}, " +
                        //     $"ID: {openRow.Field<int?>("ID")}, " +
                        //     $"CounterpartyCode: {openRow.Field<string>("CounterpartyCode")}, " +
                        //     $"AccountNumber: {openRow.Field<string>("AccountNumber")}, " +
                        //     $"TradeDate: {openRow.Field<DateTime>("TradeDate")}, " +
                        //     $"ExecutionTime: {openRow.Field<string>("ExecutionTime")}, " +
                        //     $"BuySell: {openRow.Field<string>("BuySell")}, " +
                        //     $"Quantity: {openRow.Field<decimal?>("Quantity")}, " +
                        //     $"Price: {openRow.Field<decimal?>("DealtPrice")}, " +
                        //     $"Symbol: {openRow.Field<string>("Symbol")}, " +
                        //     $"Cusip: {openRow.Field<string>("Cusip")}, " +
                        //     $"TradeMarkedAs: {openRow.Field<string>("TradeMarkedAs")}, " +
                        //     $"UnrealizedPNL: {openRow.Field<decimal?>("UnrealizedPNL")}, " +
                        //     $"RealizedPNL: {openRow.Field<decimal?>("RealizedPNL")}, " +
                        //     $"TotalRealizedPNL: {openRow.Field<decimal?>("TotalRealizedPNL")}, " +
                        //     $"GrossConsideration: {openRow.Field<decimal>("GrossConsideration")}" +
                        //     $"SequenceNumber: {openRow.Field<int?>("SequenceNumber")}, " +
                        //     $"UnrealizedQuantity: {openRow.Field<decimal?>("UnrealizedQuantity")}, " +
                        //     $"ModifiedDate: {openRow.Field<DateTime?>("ModifiedDate")}, "
                        // );

                        //set modified date of open row to maxDate
                        openRow.SetField<DateTime>("ModifiedDate", maxDate);

                        decimal qty = openRow.Field<decimal?>("UnrealizedQuantity") ?? 0;
                        // decimal cost = openRow.Field<decimal?>("GrossConsideration") ?? 0; //this should be adjusted based on unrealizedQuantity (grossConsideration is based on full quantity). using price and unrealizedQuantity to calculate cost.
                        decimal price = openRow.Field<decimal?>("DealtPrice") ?? 0;
                        decimal cost = qty * price; // Calculate cost based on price and unrealizedQuantity

                        cumulativeQuantity += qty;
                        costBasis += cost;
                    }


                    // Start iterating through the group in order of execution time and calculate realized PNL using the cumulative quantity and cost basis
                    for (int i = 0; i < thisGroup.Count; i++)
                    {
                        var row = thisGroup[i];

                        logger.Info($"Current Row Start | ID: {row.Field<int?>("ID")}, " +
                            $"GroupID: {row.Field<int?>("GroupID")}, " +
                            $"Symbol: {row.Field<string>("Symbol")}, " +
                            $"Cusip: {row.Field<string>("Cusip")}, " +
                            $"SequenceNumber: {row.Field<int?>("SequenceNumber")}, " +
                            $"UnrealizedQuantity: {row.Field<decimal?>("UnrealizedQuantity")}, " +
                            $"CumulativeQuantity: {cumulativeQuantity}, " +
                            $"CostBasis: {costBasis}");

                        if (row.Field<bool?>("IsProcessed") == true)
                        {
                            logger.Info($"Row with ID: {row.Field<int?>("ID")} and SequenceNumber: {row.Field<int?>("SequenceNumber")} has already been processed. Skipping.");
                            continue; // Skip rows that have already been processed
                        }

                        // Assign the sequence number to the row
                        row.SetField<int>("SequenceNumber", maxSequenceNumber);
                        maxSequenceNumber++; // Increment for the next row

                        // Assign Unrealized Quantity to row Quantity (it should already be but just in case)
                        row.SetField<decimal?>("UnrealizedQuantity", row.Field<decimal>("Quantity")); //unrealized qty needs to be set 

                        // if CounterpartyChargingRole is C, set TradeMarkedAs to 'Client', else set to 'Street' (when we compare trades, if a street doesn't pair with a client, it should say prop)
                        if (row.Field<string>("CounterpartyChargingRole") == "C")
                        {
                            row.SetField<string>("TradeMarkedAs", "Client");
                        }
                        else
                        {
                            row.SetField<string>("TradeMarkedAs", "Street");
                        }

                        // Check if cumulative quantity and row quantity have the same sign; set as opening trade
                        if (cumulativeQuantity * row.Field<decimal>("UnrealizedQuantity") >= 0)
                        {
                            // Create new row, copy data from row, and Add to open position list
                            DataRow newRow = row.Table.NewRow();
                            newRow.ItemArray = (row.ItemArray.Clone() as object[]) ?? Array.Empty<object>();

                            // Assign ModifiedDate to TradeDate
                            newRow.SetField<DateTime?>("ModifiedDate", row.Field<DateTime>("TradeDate"));

                            openPositions.Add(newRow);

                            // Assign Unrealized PNL to row
                            row.SetField<decimal?>("UnrealizedPNL", CalculateUnrealizedPNL(row));

                        }
                        else  // if different sign
                        {

                            // Match against open positions
                            MatchAgainstOpenPositions(row, openPositions, thisGroup, carryForwardRows, maxDate);

                        }

                        decimal qty = row.Field<decimal?>("Quantity") ?? 0;
                        decimal price = row.Field<decimal?>("DealtPrice") ?? 0;
                        decimal cost = qty * price;
                        cumulativeQuantity += qty;
                        costBasis += cost;


                        row.SetField<decimal>("CumulativeQuantity", cumulativeQuantity);
                        row.SetField<decimal>("CostBasis", costBasis);
                        row.SetField<bool>("IsProcessed", true);

                        // log the cost basis and cumulative quantity along with the id, symbol, cusip, sequence number, groupid, and unrealizedquntity
                        logger.Info($"Current Row End | ID: {row.Field<int?>("ID")}, " +
                            $"GroupID: {row.Field<int?>("GroupID")}, " +
                            $"Symbol: {row.Field<string>("Symbol")}, " +
                            $"Cusip: {row.Field<string>("Cusip")}, " +
                            $"SequenceNumber: {row.Field<int?>("SequenceNumber")}, " +
                            $"UnrealizedQuantity: {row.Field<decimal?>("UnrealizedQuantity")}, " +
                            $"CumulativeQuantity: {cumulativeQuantity}, " +
                            $"CostBasis: {costBasis}");
                    }

                    /*
                    PROP PNL REASSIGNMENT

                    some realized pnl will be for where counterparty = Prop. 
                    total this and split it amongst the other counterparty codes proportionally to their quantity traded. 
                    use columns ReassignedPNL for the amount that is reassigned and TotalRealizedPNL to sum up RealizedPNL and ReassignedPNL. 
                    then make PNL column what TotalRealizedPNL is.
                    */

                    // If the group only has rows where CounterpartyChargingRole is "B" (Prop)
                    if (thisGroup.All(row => row.Field<string>("CounterpartyChargingRole") == "B"))
                    {
                        foreach (var row in thisGroup)
                        {
                            decimal realizedPNL = row.Field<decimal?>("RealizedPNL") ?? 0;
                            decimal marketCharges = row.Field<decimal?>("MarketCharges") ?? 0;

                            row.SetField<decimal>("TotalRealizedPNL", realizedPNL + marketCharges);
                            row.SetField<decimal>("PNL", realizedPNL + marketCharges);

                            row.SetField<string>("TradeMarkedAs", "Prop");

                            // Set UnrealizedPNL, RealizedPNL to 0 if it is NULL
                            row.SetField<decimal>("UnrealizedPNL", row.Field<decimal?>("UnrealizedPNL") ?? 0);
                            row.SetField<decimal>("RealizedPNL", row.Field<decimal?>("RealizedPNL") ?? 0);

                        }
                    }
                    else //street + prop trades
                    {
                        // Calculate total realized PNL where CounterpartyChargingRole is "B"
                        decimal totalRealizedPNLProp = thisGroup
                            .Where(row => row.Field<string>("CounterpartyChargingRole") == "B") //broker (street)
                            .Sum(row => row.Field<decimal?>("RealizedPNL") ?? 0);

                        // Calculate total quantity for non-"Prop" counterparties
                        decimal totalQuantityNonProp = thisGroup
                            .Where(row => row.Field<string>("CounterpartyChargingRole") == "C") //client
                                                                                                //.Sum(row => Math.Abs(row.Field<decimal>("Quantity") - row.Field<decimal>("UnrealizedQuantity"))); //realized qty s/b Quantity - UnrealizedQty
                            .Sum(row => Math.Abs(row.Field<decimal>("Quantity")));

                        // Distribute the realized PNL proportionally to client counterparties
                        foreach (var row in thisGroup.Where(row => row.Field<string>("CounterpartyChargingRole") == "C"))
                        {
                            // If totalQuantityNonProp is 0, skip the proportional calculation to avoid division by zero
                            if (totalQuantityNonProp == 0)
                            {
                                logger.Info("Total quantity for non-Prop counterparties is 0, skipping proportional PNL calculation.");
                                continue;
                            }
                            // Calculate proportional PNL based on the row's quantity and total quantity for non-Prop counterparties
                            decimal proportionalPNL = (Math.Abs(row.Field<decimal>("Quantity") - row.Field<decimal>("UnrealizedQuantity")) / totalQuantityNonProp) * totalRealizedPNLProp;
                            decimal reassignedPNL = row.Field<decimal?>("ReassignedPNL") ?? 0;
                            row.SetField<decimal>("ReassignedPNL", reassignedPNL + proportionalPNL);

                            decimal realizedPNL = row.Field<decimal?>("RealizedPNL") ?? 0;

                            decimal marketCharges = (row.Field<decimal?>("MarketCharges") ?? 0);

                            decimal totalRealizedPNL = realizedPNL + reassignedPNL + proportionalPNL + marketCharges;
                            row.SetField<decimal>("TotalRealizedPNL", totalRealizedPNL);
                            row.SetField<decimal>("PNL", totalRealizedPNL);

                            // Calculate Percentage of Group
                            row.SetField<decimal>("PercentageOfGroup", (Math.Abs(row.Field<decimal>("Quantity") - row.Field<decimal>("UnrealizedQuantity")) / totalQuantityNonProp) * 100);

                            // Set UnrealizedPNL to 0 if it is NULL
                            row.SetField<decimal>("UnrealizedPNL", row.Field<decimal?>("UnrealizedPNL") ?? 0);
                            row.SetField<decimal>("RealizedPNL", row.Field<decimal?>("RealizedPNL") ?? 0);
                        }
                    }
                }

                // Copy the processed group to the results table
                DataTable tempTable = thisGroup.CopyToDataTable();
                resultsTable.Merge(tempTable);
            }


            // Convert carryForwardRows back to a DataTable with the same schema as carryForwardTable
            DataTable carryForwardResultTable = carryForwardTable.Clone();
            foreach (var row in carryForwardRows)
            {
                carryForwardResultTable.ImportRow(row);
            }

            // Create a DataSet and add both tables
            DataSet resultSet = new DataSet();
            resultSet.Tables.Add(resultsTable);
            resultSet.Tables.Add(carryForwardResultTable);

            logger.Info($"Number of rows in resultsTable: {resultsTable.Rows.Count}");
            logger.Info($"Number of rows in carryForwardResultTable: {carryForwardResultTable.Rows.Count}");

            return resultSet;
        }

        private static void MatchAgainstOpenPositions(DataRow currentRow, List<DataRow> openPositions, List<DataRow> thisGroup, List<DataRow> carryFowardList, DateTime maxDate)
        {

            if (currentRow == null)
            {
                logger.Error("currentRow is null.");
                throw new ArgumentNullException(nameof(currentRow), "currentRow cannot be null.");
            }

            if (openPositions == null)
            {
                logger.Error("openPositions is null.");
                throw new ArgumentNullException(nameof(openPositions), "openPositions cannot be null.");
            }

            if (thisGroup == null)
            {
                logger.Error("thisGroup is null.");
                throw new ArgumentNullException(nameof(thisGroup), "thisGroup cannot be null.");
            }


            decimal currentQty = currentRow.Field<decimal>("UnrealizedQuantity");
            decimal realizedPNL;

            if (openPositions != null || openPositions.Count > 0)
            {

                // go through open positions in first in first out fashion
                foreach (DataRow openRow in openPositions.ToList())
                {
                    decimal openQty = openRow.Field<decimal>("UnrealizedQuantity");
                    // decimal openNetConsideration = openRow.Field<decimal>("GrossConsideration");

                    // if current quantity is 0, break out of the loop
                    if (currentQty == 0)
                    {
                        break;  // break out of the foreach loop
                    }

                    // if open quantity is 0, remove the open position and continue with next open position
                    if (openQty == 0)
                    {
                        openPositions.Remove(openRow);
                        continue;
                    }

                    decimal proportion = Math.Abs(currentQty / openQty);

                    // if current qty is >= openPos, we pair it off, reduce the current qty, remove that openPosition and go to the next openPosition
                    if (Math.Abs(currentQty) >= Math.Abs(openQty))
                    {

                        logger.Info("pairing trade against openPosition: ");
                        logger.Info("openRow Side: ID: " + openRow.Field<int?>("ID") +
                            ", GroupID: " + openRow.Field<int?>("GroupID") +
                            ", CounterpartyCode: " + openRow.Field<string>("CounterpartyCode") +
                            ", TradeDate: " + openRow.Field<DateTime>("TradeDate") +
                            ", ExecutionTime: " + openRow.Field<string>("ExecutionTime") +
                            ", BuySell: " + openRow.Field<string>("BuySell") +
                            ", Quantity: " + openRow.Field<decimal?>("Quantity") +
                            ", Symbol: " + openRow.Field<string>("Symbol") +
                            ", Cusip: " + openRow.Field<string>("Cusip") +
                            ", Price: " + openRow.Field<decimal?>("DealtPrice") +
                            ", TradeMarkedAs: " + openRow.Field<string>("TradeMarkedAs") +
                            ", SequenceNumber: " + openRow.Field<int?>("SequenceNumber") +
                            ", UnrealizedQuantity: " + openRow.Field<decimal?>("UnrealizedQuantity") +
                            ", UnrealizedPNL: " + openRow.Field<decimal?>("UnrealizedPNL") +
                            ", RealizedPNL: " + openRow.Field<decimal?>("RealizedPNL"));

                        logger.Info("currentRow Side: ID: " + currentRow.Field<int?>("ID") +
                            ", GroupID: " + currentRow.Field<int?>("GroupID") +
                            ", CounterpartyCode: " + currentRow.Field<string>("CounterpartyCode") +
                            ", TradeDate: " + openRow.Field<DateTime>("TradeDate") +
                            ", ExecutionTime: " + currentRow.Field<string>("ExecutionTime") +
                            ", BuySell: " + currentRow.Field<string>("BuySell") +
                            ", Quantity: " + currentRow.Field<decimal?>("Quantity") +
                            ", Symbol: " + currentRow.Field<string>("Symbol") +
                            ", Cusip: " + currentRow.Field<string>("Cusip") +
                            ", Price: " + currentRow.Field<decimal?>("DealtPrice") +
                            ", TradeMarkedAs: " + currentRow.Field<string>("TradeMarkedAs") +
                            ", SequenceNumber: " + currentRow.Field<int?>("SequenceNumber") +
                            ", UnrealizedQuantity: " + currentRow.Field<decimal?>("UnrealizedQuantity") +
                            ", UnrealizedPNL: " + currentRow.Field<decimal?>("UnrealizedPNL") +
                            ", RealizedPNL: " + currentRow.Field<decimal?>("RealizedPNL"));

                        // find openRow in thisGroup and assign PNLs
                        realizedPNL = CalculateRealizedPNL(currentRow, openRow);

                        // Match openRow in thisGroup or carryFowardList by ID, TradeDate, and Symbol to avoid ID overlap between lists
                        DataRow matchingOpenRow = thisGroup.FirstOrDefault(row =>
                                row.Field<int>("ID") == openRow.Field<int>("ID") &&
                                row.Field<DateTime>("TradeDate") == openRow.Field<DateTime>("TradeDate") &&
                                row.Field<string>("Symbol") == openRow.Field<string>("Symbol"))
                            ?? carryFowardList.FirstOrDefault(row =>
                                row.Field<int>("ID") == openRow.Field<int>("ID") &&
                                row.Field<DateTime>("TradeDate") == openRow.Field<DateTime>("TradeDate") &&
                                row.Field<string>("Symbol") == openRow.Field<string>("Symbol"));

                        if (matchingOpenRow == null)
                        {
                            logger.Error("Matching open row not found for ID: " + openRow.Field<int>("ID") +
                                ", TradeDate: " + openRow.Field<DateTime>("TradeDate") +
                                ", Symbol: " + openRow.Field<string>("Symbol"));
                            throw new InvalidOperationException("Matching open row not found.");
                        }

                        AssignPNLs(currentRow, matchingOpenRow, realizedPNL);

                        // set unrealized quantity | pairing off the open position
                        currentRow.SetField<decimal?>("UnrealizedQuantity", currentRow.Field<decimal>("UnrealizedQuantity") + openQty);
                        openRow.SetField<decimal?>("UnrealizedQuantity", 0);
                        matchingOpenRow.SetField<decimal?>("UnrealizedQuantity", 0);

                        // if matchingOpenRow is in carryForwardList, then update the modifiedDate field to maxDate
                        if (carryFowardList.Contains(matchingOpenRow))
                        {
                            openRow.SetField<DateTime>("ModifiedDate", maxDate);
                            matchingOpenRow.SetField<DateTime>("ModifiedDate", maxDate);
                            //currentRow.SetField<DateTime>("ModifiedDate", currentRow.Field<DateTime>("TradeDate"));
                        }

                        // log the pnl assigned to the currentRow and openRow
                        logger.Info("Realized PNL assigned to openRow: " + (openRow.Field<decimal?>("RealizedPNL") ?? 0) +
                            " to CounterpartyCode: " + openRow.Field<string>("CounterpartyCode") +
                            ", SequenceNumber: " + openRow.Field<int?>("SequenceNumber"));
                        logger.Info("Realized PNL assigned to currentRow: " + (currentRow.Field<decimal?>("RealizedPNL") ?? 0) +
                            " to CounterpartyCode: " + currentRow.Field<string>("CounterpartyCode") +
                            ", SequenceNumber: " + currentRow.Field<int?>("SequenceNumber"));
                        currentQty = currentQty + openQty; // reduce the current quantity by the amount it was paired off with so that it can be matched against the next open position
                        openPositions.Remove(openRow); // remove the open position that was paired off
                    }
                    // if current quantity is < open quantity
                    else if (Math.Abs(currentQty) < Math.Abs(openQty))
                    {
                        // split openRow into two rows by creating a partial
                        DataRow partialOpenRow = openRow.Table.NewRow();
                        partialOpenRow.ItemArray = (openRow.ItemArray.Clone() as object[]) ?? Array.Empty<object>();

                        // set openRow to pair off with the current row (Quantity, GrossConsideration)
                        openRow.SetField<decimal>("UnrealizedQuantity", openQty - currentQty);
                        // openRow.SetField<decimal?>("GrossConsideration", openNetConsideration * proportion);

                        // find openRow in thisGroup and assign PNLs
                        realizedPNL = CalculateRealizedPNL(currentRow, openRow);

                        // Match openRow in thisGroup or carryFowardList by ID, TradeDate, and Symbol to avoid ID overlap between lists
                        DataRow matchingOpenRow = thisGroup.FirstOrDefault(row =>
                                row.Field<int>("ID") == openRow.Field<int>("ID") &&
                                row.Field<DateTime>("TradeDate") == openRow.Field<DateTime>("TradeDate") &&
                                row.Field<string>("Symbol") == openRow.Field<string>("Symbol"))
                            ?? carryFowardList.FirstOrDefault(row =>
                                row.Field<int>("ID") == openRow.Field<int>("ID") &&
                                row.Field<DateTime>("TradeDate") == openRow.Field<DateTime>("TradeDate") &&
                                row.Field<string>("Symbol") == openRow.Field<string>("Symbol"));

                        if (matchingOpenRow == null)
                        {
                            logger.Error("Matching open row not found for ID: " + openRow.Field<int>("ID") +
                                ", TradeDate: " + openRow.Field<DateTime>("TradeDate") +
                                ", Symbol: " + openRow.Field<string>("Symbol"));
                            throw new InvalidOperationException("Matching open row not found.");
                        }

                        AssignPNLs(currentRow, matchingOpenRow, realizedPNL);

                        // set unrealized quantity | pairing off the currentQty 
                        currentRow.SetField<decimal?>("UnrealizedQuantity", 0);
                        openRow.SetField<decimal?>("UnrealizedQuantity", openRow.Field<decimal>("UnrealizedQuantity") + currentQty);
                        matchingOpenRow.SetField<decimal?>("UnrealizedQuantity", matchingOpenRow.Field<decimal>("UnrealizedQuantity") + currentQty);

                        // if matchingOpenRow is in carryForwardList, then update the modifiedDate field to maxDate
                        if (carryFowardList.Contains(matchingOpenRow))
                        {
                            openRow.SetField<DateTime>("ModifiedDate", maxDate);
                            matchingOpenRow.SetField<DateTime>("ModifiedDate", maxDate);
                            partialOpenRow.SetField<DateTime>("ModifiedDate", maxDate);
                        }
                        else
                        {
                            openRow.SetField<DateTime>("ModifiedDate", currentRow.Field<DateTime>("TradeDate"));
                            matchingOpenRow.SetField<DateTime>("ModifiedDate", currentRow.Field<DateTime>("TradeDate"));
                            partialOpenRow.SetField<DateTime>("ModifiedDate", currentRow.Field<DateTime>("TradeDate"));
                        }
                        currentRow.SetField<DateTime>("ModifiedDate", currentRow.Field<DateTime>("TradeDate"));

                        // adjust the partialOpenRow to show what is left of the openPosition (Quantity, NetConsideration)
                        partialOpenRow.SetField<decimal>("UnrealizedQuantity", partialOpenRow.Field<decimal>("UnrealizedQuantity") - currentQty);
                        //partialOpenRow.SetField<decimal?>("GrossConsideration", openNetConsideration - (openNetConsideration * proportion));
                        partialOpenRow.SetField<decimal?>("UnrealizedPNL", CalculateUnrealizedPNL(partialOpenRow));

                        // log the pnl assigned to the currentRow and openRow
                        logger.Info("Realized PNL assigned to openRow: " + (openRow.Field<decimal?>("RealizedPNL") ?? 0) + " to CounterpartyCode: " + openRow.Field<string>("CounterpartyCode") + ", SequenceNumber: " + currentRow.Field<int?>("SequenceNumber"));
                        logger.Info("Realized PNL assigned to currentRow: " + (currentRow.Field<decimal?>("RealizedPNL") ?? 0) + " to CounterpartyCode: " + currentRow.Field<string>("CounterpartyCode") + ", SequenceNumber: " + currentRow.Field<int?>("SequenceNumber"));

                        // remove the openRow from the openPositions
                        openPositions.Remove(openRow);

                        // add the partialOpenRow to the openPositions (to first index)
                        openPositions.Insert(0, partialOpenRow);

                        // log the new open position
                        logger.Info(
                            $"New Open Position from partialOpenRow: " +
                            $"ID: {partialOpenRow.Field<int?>("ID")}, " +
                            $"GroupID: {partialOpenRow.Field<int?>("GroupID")}, " +
                            $"Symbol: {partialOpenRow.Field<string>("Symbol")}, " +
                            $"Cusip: {partialOpenRow.Field<string>("Cusip")}, " +
                            $"SequenceNumber: {partialOpenRow.Field<int?>("SequenceNumber")}, " +
                            $"UnrealizedQuantity: {partialOpenRow.Field<decimal?>("UnrealizedQuantity")}, " +
                            $"CumulativeQuantity: {partialOpenRow.Field<decimal?>("CumulativeQuantity")}, " +
                            $"CostBasis: {partialOpenRow.Field<decimal?>("CostBasis")}"
                        );

                        // set currentQty to 0 since we have matched it off 
                        currentQty = 0;
                        break;  // break out of the foreach loop
                    }
                }
            }

            // if there is still a current qty and no more openPos, then we treat that remainder as a new open position
            if (currentQty != 0)
            {
                DataRow newOpenRow = currentRow.Table.NewRow();
                newOpenRow.ItemArray = currentRow.ItemArray.Clone() as object[] ?? Array.Empty<object>();
                newOpenRow.SetField<DateTime>("ModifiedDate", currentRow.Field<DateTime>("TradeDate"));
                newOpenRow.SetField<decimal>("UnrealizedQuantity", currentQty);

                openPositions.Insert(0, newOpenRow);

                //log the new open position
                logger.Info(
                    $"New Open Position from currentRow: " +
                    $"ID: {newOpenRow.Field<int?>("ID")}, " +
                    $"GroupID: {newOpenRow.Field<int?>("GroupID")}, " +
                    $"Symbol: {newOpenRow.Field<string>("Symbol")}, " +
                    $"Cusip: {newOpenRow.Field<string>("Cusip")}, " +
                    $"SequenceNumber: {newOpenRow.Field<int?>("SequenceNumber")}, " +
                    $"UnrealizedQuantity: {newOpenRow.Field<decimal?>("UnrealizedQuantity")}, " +
                    $"CumulativeQuantity: {newOpenRow.Field<decimal?>("CumulativeQuantity")}, " +
                    $"CostBasis: {newOpenRow.Field<decimal?>("CostBasis")}"
                );

                // decimal proportion = Math.Abs(currentQty) / Math.Abs(currentRow.Field<decimal>("Quantity"));
                // newOpenRow.SetField<decimal?>("GrossConsideration", currentRow.Field<decimal>("GrossConsideration") * proportion);

                newOpenRow.SetField<decimal?>("UnrealizedPNL", CalculateUnrealizedPNL(newOpenRow));
            }
        }

        private static List<DataRow> findOpenPositions(List<DataRow> thisGroup, List<DataRow> carryForwardRows)
        {
            var firstRow = thisGroup.Any() ? thisGroup.First() : null;
            if (firstRow == null)
            {
                return null;
            }
            else
            {
                logger.Info("Finding Open Positions for AccountNumber: " + firstRow.Field<string>("AccountNumber") + ", Cusip: " + firstRow.Field<string>("Cusip") + ", TradeDate: " + firstRow.Field<DateTime>("TradeDate"));

                string accountNumber = firstRow.Field<string>("AccountNumber") ?? string.Empty;
                string cusip = firstRow.Field<string>("Cusip") ?? string.Empty;
                DateTime tradeDate = firstRow.Field<DateTime>("TradeDate");

                // Find all carry forward rows for this account/cusip where TradeDate is less than or equal to the current tradeDate and UnrealizedQuantity != 0
                var openPositions = carryForwardRows
                    .Where(row =>
                        row.Field<string>("AccountNumber") == accountNumber &&
                        row.Field<string>("Cusip") == cusip &&
                        row.Field<decimal?>("UnrealizedQuantity") != 0 &&
                        row.Field<DateTime>("TradeDate") <= tradeDate)
                    .OrderBy(row => row.Field<DateTime>("TradeDate"))
                    .ThenBy(row => row.Field<string>("ExecutionTime"))
                    .ToList();

                // print how many open positions were found
                logger.Info("Found " + openPositions.Count + " open positions for AccountNumber: " + accountNumber + ", Cusip: " + cusip + ", TradeDate: " + tradeDate);

                return openPositions;
            }
        }

        private static decimal CalculateUnrealizedPNL(DataRow row)
        {

            decimal unrealizedPNL = 0;

            if (row == null)
            {
                logger.Error("DataRow cannot be null.");
                throw new ArgumentNullException(nameof(row), "DataRow cannot be null.");
            }

            if (row.Field<decimal>("UnrealizedQuantity") == 0)
            {
                // If UnrealizedQuantity is 0, return 0
                return 0;
            }

            // if (row.Field<string>("BuySell") == "B" && row.Field<decimal?>("ClosePrice").HasValue && row.Field<decimal?>("NetPrice").HasValue)
            // {
            //     // unrealizedPNL = ((row.Field<decimal>("ClosePrice") * row.Field<decimal>("Quantity")) - row.Field<decimal>("GrossConsideration")); 
            //     unrealizedPNL = ((row.Field<decimal>("ClosePrice") * row.Field<decimal>("UnrealizedQuantity")) - row.Field<decimal>("GrossConsideration"));
            // }
            // else if (row.Field<string>("BuySell") == "S" && row.Field<decimal?>("NetPrice").HasValue && row.Field<decimal?>("ClosePrice").HasValue)
            // {
            //     // unrealizedPNL = (row.Field<decimal>("GrossConsideration") - (row.Field<decimal>("Quantity") * row.Field<decimal>("ClosePrice"))) * -1;
            //     unrealizedPNL = (row.Field<decimal>("GrossConsideration") - (row.Field<decimal>("UnrealizedQuantity") * row.Field<decimal>("ClosePrice"))) * -1;
            // }

            decimal dealtPrice = row.Field<decimal?>("DealtPrice") ?? 0;
            decimal closePrice = row.Field<decimal?>("ClosePrice") ?? 0;
            decimal unrealizedQty = row.Field<decimal>("UnrealizedQuantity");

            if (row.Field<string>("BuySell") == "B" && closePrice != 0 && dealtPrice != 0)
            {
                unrealizedPNL = (closePrice - dealtPrice) * unrealizedQty;
            }
            else if (row.Field<string>("BuySell") == "S" && closePrice != 0 && dealtPrice != 0)
            {
                unrealizedPNL = (dealtPrice - closePrice) * unrealizedQty;
            }

            unrealizedPNL = Math.Round(unrealizedPNL, 2);

            return unrealizedPNL;
        }

        private static decimal CalculateRealizedPNL(DataRow row, DataRow openRow)
        {
            decimal realizedPNL = 0;

            // decimal proportion = Math.Abs(openRow.Field<decimal>("UnrealizedQuantity")) / Math.Abs(row.Field<decimal>("UnrealizedQuantity"));

            // if (proportion < 1)
            // {
            //     realizedPNL = ((row.Field<decimal>("GrossConsideration") * proportion) + openRow.Field<decimal>("GrossConsideration")) * -1;
            // }
            // else
            // {
            //     realizedPNL = (row.Field<decimal>("GrossConsideration") + (openRow.Field<decimal>("GrossConsideration") * proportion)) * -1;
            // }

            decimal rowPrice = row.Field<decimal?>("DealtPrice") ?? 0;
            decimal openRowPrice = openRow.Field<decimal?>("DealtPrice") ?? 0;
            decimal rowUnrealizedQty = row.Field<decimal?>("UnrealizedQuantity") ?? 0;
            decimal openRowUnrealizedQty = openRow.Field<decimal?>("UnrealizedQuantity") ?? 0;

            // Realized PNL is the difference in price times the matched quantity
            // The matched quantity is the lesser of the absolute unrealized quantities
            decimal matchedQty = Math.Min(Math.Abs(rowUnrealizedQty), Math.Abs(openRowUnrealizedQty));

            // Determine direction based on Buy/Sell
            // If row is Sell, realized PNL = (SellPrice - BuyPrice) * matchedQty
            // If row is Buy, realized PNL = (BuyPrice - SellPrice) * matchedQty

            string rowSide = row.Field<string>("BuySell");
            string openRowSide = openRow.Field<string>("BuySell");

            if (rowSide == "S" && openRowSide == "B")
            {
                realizedPNL = (rowPrice - openRowPrice) * matchedQty;
            }
            else if (rowSide == "B" && openRowSide == "S")
            {
                realizedPNL = (openRowPrice - rowPrice) * matchedQty;
            }
            else
            {
                // Fallback: use price difference times matched quantity
                realizedPNL = (rowPrice - openRowPrice) * matchedQty;
            }

            realizedPNL = Math.Round(realizedPNL, 2);

            return realizedPNL;
        }

        private static void AssignPNLs(DataRow currentRow, DataRow openRow, decimal realizedPNL)
        {
            string counterparty = currentRow.Field<string>("CounterpartyChargingRole")?.Trim() ?? string.Empty;
            string openCounterparty = openRow.Field<string>("CounterpartyChargingRole")?.Trim() ?? string.Empty;

            if (counterparty == "B" && openCounterparty == "B") //if both are street side then split the pnl, market both as prop
            {
                currentRow.SetField<decimal?>("RealizedPNL", (currentRow.Field<decimal?>("RealizedPNL") ?? 0) + realizedPNL / 2);
                openRow.SetField<decimal?>("RealizedPNL", (openRow.Field<decimal?>("RealizedPNL") ?? 0) + realizedPNL / 2);

                currentRow.SetField<string>("TradeMarkedAs", "Prop");
                openRow.SetField<string>("TradeMarkedAs", "Prop");
            }
            else if (counterparty == "B" || openCounterparty == "B") //if either is street, mark that side as street
            {
                if (counterparty == "B") //check if current side is street, if so then assign to open
                {
                    openRow.SetField<decimal?>("RealizedPNL", (openRow.Field<decimal?>("RealizedPNL") ?? 0) + realizedPNL);
                    currentRow.SetField<string>("TradeMarkedAs", "Street");
                }
                else //else assign to current
                {
                    currentRow.SetField<decimal?>("RealizedPNL", (currentRow.Field<decimal?>("RealizedPNL") ?? 0) + realizedPNL);
                    openRow.SetField<string>("TradeMarkedAs", "Street");
                }
            }
            else //else just split the pnl (this case should be client paired with client)
            {
                currentRow.SetField<decimal?>("RealizedPNL", (currentRow.Field<decimal?>("RealizedPNL") ?? 0) + realizedPNL / 2);
                openRow.SetField<decimal?>("RealizedPNL", (openRow.Field<decimal?>("RealizedPNL") ?? 0) + realizedPNL / 2);
            }


            currentRow.SetField<decimal?>("UnrealizedPNL", CalculateUnrealizedPNL(currentRow));
            openRow.SetField<decimal?>("UnrealizedPNL", CalculateUnrealizedPNL(openRow));
            currentRow.SetField<decimal?>("TotalRealizedPNL", (currentRow.Field<decimal?>("TotalRealizedPNL") ?? 0) + (currentRow.Field<decimal?>("RealizedPNL") ?? 0) + (currentRow.Field<decimal?>("MarketCharges") ?? 0));
            openRow.SetField<decimal?>("TotalRealizedPNL", (openRow.Field<decimal?>("TotalRealizedPNL") ?? 0) + (openRow.Field<decimal?>("RealizedPNL") ?? 0) + (openRow.Field<decimal?>("MarketCharges") ?? 0));
            currentRow.SetField<decimal?>("PNL", (currentRow.Field<decimal?>("TotalRealizedPNL") ?? 0));
            openRow.SetField<decimal?>("PNL", (openRow.Field<decimal?>("TotalRealizedPNL") ?? 0));

            // Set UnrealizedPNL, RealizedPNL to 0 if it is NULL
            currentRow.SetField<decimal?>("UnrealizedPNL", currentRow.Field<decimal?>("UnrealizedPNL") ?? 0);
            openRow.SetField<decimal?>("UnrealizedPNL", openRow.Field<decimal?>("UnrealizedPNL") ?? 0);
            currentRow.SetField<decimal?>("RealizedPNL", currentRow.Field<decimal?>("RealizedPNL") ?? 0);
            openRow.SetField<decimal?>("RealizedPNL", openRow.Field<decimal?>("RealizedPNL") ?? 0);
        }

        private static void FindTradesWithinXSeconds(IEnumerable<DataRow> group, int seconds)
        {
            //logger.Info("Finding Trades Within X Seconds");

            // Find buys and sells within sortedGroup that have same quantity and have near executionTimes
            var sortedGroupBuys = group.Where(row => row.Field<string>("BuySell") == "B" && row.Field<int?>("MatchingTradeID") == null).OrderBy(row => row.Field<string>("ExecutionTime")).ToArray();
            var sortedGroupSells = group.Where(row => row.Field<string>("BuySell") == "S" && row.Field<int?>("MatchingTradeID") == null).OrderBy(row => row.Field<string>("ExecutionTime")).ToArray();

            //foreach (var buy in sortedGroupBuys)
            for (int i = 0; i < sortedGroupBuys.Length; i++)
            {
                var buy = sortedGroupBuys[i];

                //foreach (var sell in sortedGroupSells)
                for (int j = 0; j < sortedGroupSells.Length; j++)
                {
                    var sell = sortedGroupSells[j];

                    // if sell has already been processed, skip
                    if (sell.Field<bool?>("IsProcessed") == true)
                    {
                        continue;
                    }

                    //if quantities are same and both trades have not been processed
                    if (buy.Field<decimal>("Quantity") == Math.Abs(sell.Field<decimal>("Quantity")) && buy.Field<bool?>("IsProcessed") == false && sell.Field<bool?>("IsProcessed") == false)
                    {
                        if (Math.Abs(double.Parse(buy.Field<string>("ExecutionTime")) - double.Parse(sell.Field<string>("ExecutionTime"))) <= seconds)
                        {
                            //use logger.Info to log both the buy side trade and the sell side trade, include, side, quantity, countperpartycode, execTime, symbol, cusip, groupId
                            logger.Info("Matched Trade Within X Seconds: ");
                            logger.Info("Buy Side: ID: " + buy.Field<int>("ID") + ", CounterpartyCode: " + buy.Field<string>("CounterpartyCode") + ", ExecutionTime: " + buy.Field<string>("ExecutionTime") + ", BuySell: " + buy.Field<string>("BuySell") + ", Quantity: " + buy.Field<decimal>("Quantity") + ", Symbol: " + buy.Field<string>("Symbol") + ", Cusip: " + buy.Field<string>("Cusip") + ", GroupID: " + buy.Field<int>("GroupID"));
                            logger.Info("Sell Side: ID: " + sell.Field<int>("ID") + ", CounterpartyCode: " + sell.Field<string>("CounterpartyCode") + ", ExecutionTime: " + sell.Field<string>("ExecutionTime") + ", BuySell: " + sell.Field<string>("BuySell") + ", Quantity: " + sell.Field<decimal>("Quantity") + ", Symbol: " + sell.Field<string>("Symbol") + ", Cusip: " + sell.Field<string>("Cusip") + ", GroupID: " + sell.Field<int>("GroupID"));


                            // Calculate and record Realized PNL
                            decimal? realizedPNL = sell.Field<decimal>("GrossConsideration") - ((sell.Field<decimal>("Quantity") / buy.Field<decimal>("Quantity")) * buy.Field<decimal>("GrossConsideration"));

                            // Assign Realized PNL to both buy and sell
                            sell.SetField<decimal?>("RealizedPNL", (sell.Field<decimal?>("RealizedPNL") == null ? 0 : sell.Field<decimal?>("RealizedPNL")) + (realizedPNL == null ? 0 : realizedPNL));
                            sell.SetField<decimal?>("PNL", (sell.Field<decimal?>("PNL") == null ? 0 : sell.Field<decimal?>("PNL")) + (realizedPNL == null ? 0 : realizedPNL));

                            // Assign MatchingTradeID to both buy and sell
                            buy.SetField<bool>("IsProcessed", true);
                            sell.SetField<bool>("IsProcessed", true);

                            // Assign MatchingTradeID to both buy and sell using the sequence number of the other side
                            buy.SetField<int?>("MatchingTradeID", sell.Field<int?>("SequenceNumber"));
                            sell.SetField<int?>("MatchingTradeID", buy.Field<int?>("SequenceNumber"));


                            // check if sides are client/street then set the TradeMarkedAs field
                            if (buy.Field<string>("CounterpartyChargingRole") == "B" && sell.Field<string>("CounterpartyChargingRole") == "B") //if both are street side then split the pnl, mark both as prop
                            {
                                buy.SetField<string>("TradeMarkedAs", "Prop");
                                sell.SetField<string>("TradeMarkedAs", "Prop");
                            }
                            else if (buy.Field<string>("CounterpartyChargingRole") == "B" || sell.Field<string>("CounterpartyChargingRole") == "B") //if either is street, mark that side as street
                            {
                                if (buy.Field<string>("CounterpartyChargingRole") == "B") //check if current side is street
                                {
                                    buy.SetField<string>("TradeMarkedAs", "Street");
                                }
                                else //else assign to current
                                {
                                    sell.SetField<string>("TradeMarkedAs", "Street");
                                }
                            }
                            else //else just split the pnl (this case should be client paired with client)
                            {
                            }

                            break; // break out of the inner foreach loop (the one iterating over sortedGroupSells) and go to next buy.
                        }
                    }
                }
            }
        }

        private static int GetMaxSequenceNumberFromPNLTable()
        {
            string connectionString = getMAXSQL2_Mitas_SQLConnString();
            using (var conn = new SqlConnection(connectionString))
            {
                conn.Open();
                using (var cmd = new SqlCommand("SELECT MAX(SequenceNumber) FROM dbo.tblPNL", conn))
                {
                    cmd.CommandType = CommandType.Text;
                    var result = cmd.ExecuteScalar();
                    return result != DBNull.Value ? Convert.ToInt32(result) : 0;
                }
            }
        }

        private static DataTable GetCarryForwardTable(string? date)
        {
            string connectionString = getMAXSQL2_Mitas_SQLConnString();
            using (var conn = new SqlConnection(connectionString))
            {
                conn.Open();
                using (var cmd = new SqlCommand("msp_GetHistoricalUnrealizedTransactions", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Clear();
                    cmd.Parameters.AddWithValue("@TradeDate", date);
                    cmd.CommandTimeout = 900;
                    using (var adapter = new SqlDataAdapter(cmd))
                    {
                        DataTable carryForwardTable = new DataTable();
                        adapter.Fill(carryForwardTable);
                        logger.Info($"CarryForwardTable record count: {carryForwardTable.Rows.Count}");
                        return carryForwardTable;
                    }
                }
            }
        }

        private static void TruncateStagingTable()
        {
            string connectionString = getMAXSQL2_Mitas_SQLConnString();
            using (var conn = new SqlConnection(connectionString))
            {
                conn.Open();
                using (var cmd = new SqlCommand("truncate table dbo.tblPNLStaging", conn))
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.ExecuteNonQuery();
                }
            }
        }

        private static void RunReconSP(string? maxDate)
        {
            string connectionString = getMAXSQL2_Mitas_SQLConnString();
            using (var conn = new SqlConnection(connectionString))
            {
                conn.Open();
                using (var cmd = new SqlCommand("msp_InsertDailyPNLReconSummary", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Clear();
                    cmd.Parameters.AddWithValue("@TradeDate", maxDate);
                    cmd.ExecuteNonQuery();
                }
            }
        }

        private static void RunStoredProc(string sp)
        {
            string connectionString = getMAXSQL2_Mitas_SQLConnString();
            using (var conn = new SqlConnection(connectionString))
            {
                conn.Open();
                using (var cmd = new SqlCommand(sp, conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.ExecuteNonQuery();
                }
            }
        }

        private static void InsertToPNLStaging(DataTable table)
        {
            using (SqlConnection conn = new SqlConnection(getMAXSQL2_Mitas_SQLConnString()))
            {
                conn.Open();
                using (SqlBulkCopy bulkCopy = new SqlBulkCopy(conn))
                {
                    bulkCopy.DestinationTableName = "dbo.tblPNLStaging";

                    try
                    {
                        logger.Info($"Inserting {table.Rows.Count} records into tblPNLStaging.");

                        // Write from the source DataTable to the destination table
                        bulkCopy.WriteToServer(table);
                        logger.Info("Data inserted successfully into tblPNLStaging.");
                    }
                    catch (Exception ex)
                    {
                        logger.Error("Error inserting data: " + ex);
                        throw;
                    }
                }
            }
        }

        private static string getFT70QueryString(List<string> distinctDates)
        {
            string query = @"
            SELECT [ID]
            ,[DateOfData]
            ,[Cusip]
            ,[AccountNumber]
            ,[ActivityType]
            ,[TradeDate]
            ,[Quantity]
            ,[BookValue]
            ,[ClosePrice]
            ,[MarketValue]
            FROM [MITAS].[dbo].[tblFT70_Summaries]
            WHERE DateOfData IN(" + string.Join(", ", distinctDates.Select(date => $"'{date}'")) + @")
  		    ";
            return query;
        }

        private static string getPNLStagingQueryString()
        {
            string query = @"
            SELECT [ID]
            ,[AccountNumber]
            ,[TradeDate]
            ,[SettlementDate]
            ,[ExecutionTime]
            ,[ReferenceNumber]
            ,[TradeID]
            ,[OrderID]
            ,[GlobalOrderID]
            ,[RootOrderID]
            ,[ParentOrderID]
            ,[CounterpartyCode]
            ,[CounterpartyType]
            ,[CounterpartyTypeQualifier]
            ,[BuySell]
            ,[Quantity]
            ,[Symbol]
            ,[Cusip]
            ,[SedlCode]
            ,[GrossPrice]
            ,[DealtPrice]
            ,[NetPrice]
            ,[GrossConsideration]
            ,[NetConsideration]
            ,[MarketCharges]
            ,[BookCode]
            ,[DealtCurrencyID]
            ,[DealingCapacity]
            ,[EnteredBy]
            ,[ExecutionVenue]
            ,[MarketID]
            ,[SettlementDays]
            ,[Version]
            ,[CumulativeQuantity]
            ,[CostBasis]
            ,[ClosePrice]
            ,[UnrealizedPNL]
            ,[RealizedPNL]
            ,[PercentageOfGroup]
            ,[ReassignedPNL]
            ,[TotalRealizedPNL]
            ,[PNL]
            ,[RepresentativeID]
            ,[RepName]
            ,[PrimaryTrader]
            ,[LockDown]
            ,[Status]
            ,[FS]
            ,[ARB]
            ,[DIV]
            ,[OTH]
            ,[GroupID]
            ,[MatchingTradeID]
            ,[IsProcessed]
            ,[IsManualEntered]
            ,[Notes]
            ,[TradeMarkedAs]
            ,[CounterpartyChargingRole]
            ,[SequenceNumber]
            ,[IsPartial]
            ,[UnrealizedQuantity]
            ,[ModifiedDate]
            FROM [MITAS].[dbo].[tblPNLStaging]
		    ";
            return query;
        }

        static string getMAXSQL2_Mitas_SQLConnString()
        {
            string connString = (new SqlConnectionStringBuilder
            {
                DataSource = "MAXSQL2",
                InitialCatalog = "MITAS",
                IntegratedSecurity = true,
                TrustServerCertificate = true
            }).ConnectionString;
            return connString;
        }

        static int ExecuteNonQuery(string connectionString, SqlCommand cmd)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    cmd.Connection = conn;
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception x)
            {
                logger.Error("Command Text: " + cmd.CommandText);
                logger.Error("Error executing non-query: " + x.Message);
                throw new DbSqlException(cmd.CommandText, x);
            }
        }

        static DataSet ExecuteQuery(string query, string connectionString, SqlCommand cmd)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        da.SelectCommand.CommandTimeout = 900;
                        DataSet ds = new DataSet();
                        da.Fill(ds);
                        return ds;
                    }
                }
            }
            catch (Exception x)
            {
                logger.Error("Query: " + query);
                logger.Error("Error executing query: " + x.Message);
                throw new DbSqlException(query, x);
            }
        }

        static DataTable ExecuteQuery(string query, string connectionString)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    using (SqlDataAdapter da = new SqlDataAdapter(query, conn))
                    {
                        da.SelectCommand.CommandTimeout = 900;
                        DataTable dt = new DataTable();
                        da.Fill(dt);
                        return dt;
                    }
                }
            }
            catch (Exception x)
            {
                logger.Error("Query: " + query);
                logger.Error("Error executing query: " + x.Message);
                throw new DbSqlException(query, x);
            }
        }

        static int ExecuteNonQuery(string query, string connectionString)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandTimeout = 900;
                        return cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception x)
            {
                logger.Error("Query: " + query);
                logger.Error("Error executing query: " + x.Message);
                throw new DbSqlException(query, x);
            }
        }

        public class DbSqlException : Exception
        {
            public DbSqlException(string sql, Exception inner)
                : base(inner.Message, inner)
            {
            }
        }
    }
}